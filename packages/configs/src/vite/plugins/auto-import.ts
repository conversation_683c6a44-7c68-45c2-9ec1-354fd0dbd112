/**
 * 自动导入配置插件
 * 为 Mattverse 应用提供组件和 API 的自动导入功能
 */
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { resolve } from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

// ES 模块中获取 __dirname
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

/**
 * 创建自动导入插件配置
 */
export function createAutoImportPlugins(options: { appName: string; dtsPath?: string }) {
  const { appName, dtsPath = 'src/auto-imports.d.ts' } = options

  const uiComponents = [
    //自定义组件
    'MattIcon',
    'MattSvg',
    'MattEmptyState',
    //shadcn-ui基础组件
    'Button',
    'Input',
    'Badge',
    'Label',
    'Slider',
    'Sonner',
    'Textarea',
    'Separator',
    'Skeleton',
    'Checkbox',
    'Switch',
    'Progress',
    //Alert
    'Alert',
    'AlertDescription',
    'AlertTitle',
    //Avatar
    'Avatar',
    'AvatarFallback',
    'AvatarImage',
    //AlertDialog
    'AlertDialog',
    'AlertDialogAction',
    'AlertDialogCancel',
    'AlertDialogContent',
    'AlertDialogDescription',
    'AlertDialogFooter',
    'AlertDialogHeader',
    'AlertDialogTitle',
    'AlertDialogTrigger',
    //Sheet
    'Sheet',
    'SheetClose',
    'SheetContent',
    'SheetDescription',
    'SheetFooter',
    'SheetHeader',
    'SheetTitle',
    'SheetTrigger',
    //Sidebar
    'Sidebar',
    'SidebarContent',
    'SidebarFooter',
    'SidebarGroup',
    'SidebarGroupAction',
    'SidebarGroupContent',
    'SidebarGroupLabel',
    'SidebarHeader',
    'SidebarInput',
    'SidebarInset',
    'SidebarMenu',
    'SidebarMenuAction',
    'SidebarMenuBadge',
    'SidebarMenuButton',
    'SidebarMenuItem',
    'SidebarMenuSkeleton',
    'SidebarMenuSub',
    'SidebarMenuSubButton',
    'SidebarMenuSubItem',
    'SidebarProvider',
    'SidebarRail',
    'SidebarSeparator',
    'SidebarTrigger',
    //Tooltip
    'Tooltip',
    'TooltipContent',
    'TooltipProvider',
    'TooltipTrigger',
    //breadcrumb
    'Breadcrumb',
    'BreadcrumbEllipsis',
    'BreadcrumbItem',
    'BreadcrumbLink',
    'BreadcrumbList',
    'BreadcrumbPage',
    'BreadcrumbSeparator',
    //Accordion
    'Accordion',
    'AccordionContent',
    'AccordionItem',
    'AccordionTrigger',
    //Calendar
    'Calendar',
    'CalendarCell',
    'CalendarCellTrigger',
    'CalendarGrid',
    'CalendarGridBody',
    'CalendarGridHead',
    'CalendarGridRow',
    'CalendarHeadCell',
    'CalendarHeader',
    'CalendarHeading',
    'CalendarNextButton',
    'CalendarPrevButton',
    //Card
    'Card',
    'CardAction',
    'CardContent',
    'CardDescription',
    'CardFooter',
    'CardHeader',
    'CardTitle',
    //Carousel
    'Carousel',
    'CarouselContent',
    'CarouselItem',
    'CarouselNext',
    'CarouselPrevious',
    //Collapsible
    'Collapsible',
    'CollapsibleContent',
    'CollapsibleTrigger',
    //Combobox
    'Combobox',
    'ComboboxAnchor',
    'ComboboxCancel',
    'ComboboxEmpty',
    'ComboboxGroup',
    'ComboboxInput',
    'ComboboxItem',
    'ComboboxItemIndicator',
    'ComboboxList',
    'ComboboxPortal',
    'ComboboxSeparator',
    'ComboboxTrigger',
    'ComboboxViewport',
    //Command
    'Command',
    'CommandDialog',
    'CommandEmpty',
    'CommandGroup',
    'CommandInput',
    'CommandItem',
    'CommandList',
    'CommandSeparator',
    'CommandShortcut',
    //ContextMenu
    'ContextMenu',
    'ContextMenuCheckboxItem',
    'ContextMenuContent',
    'ContextMenuGroup',
    'ContextMenuItem',
    'ContextMenuLabel',
    'ContextMenuPortal',
    'ContextMenuRadioGroup',
    'ContextMenuRadioItem',
    'ContextMenuSeparator',
    'ContextMenuShortcut',
    'ContextMenuSub',
    'ContextMenuSubContent',
    'ContextMenuSubTrigger',
    'ContextMenuTrigger',
    //Dialog
    'Dialog',
    'DialogClose',
    'DialogContent',
    'DialogDescription',
    'DialogFooter',
    'DialogHeader',
    'DialogOverlay',
    'DialogPortal',
    'DialogScrollContent',
    'DialogTitle',
    'DialogTrigger',
    //Drawer
    'Drawer',
    'DrawerClose',
    'DrawerContent',
    'DrawerDescription',
    'DrawerFooter',
    'DrawerHeader',
    'DrawerOverlay',
    'DrawerPortal',
    'DrawerTitle',
    'DrawerTrigger',
    //DropdownMenu
    'DropdownMenu',
    'DropdownMenuCheckboxItem',
    'DropdownMenuContent',
    'DropdownMenuGroup',
    'DropdownMenuItem',
    'DropdownMenuLabel',
    'DropdownMenuPortal',
    'DropdownMenuRadioGroup',
    'DropdownMenuRadioItem',
    'DropdownMenuSeparator',
    'DropdownMenuShortcut',
    'DropdownMenuSub',
    'DropdownMenuSubContent',
    'DropdownMenuSubTrigger',
    'DropdownMenuTrigger',
    //Form
    'Form',
    'FormField',
    'FormItem',
    'FormControl',
    'FormLabel',
    'FormMessage',
    'FormDescription',
    //HoverCard
    'HoverCard',
    'HoverCardContent',
    'HoverCardTrigger',
    //Menubar
    'Menubar',
    'MenubarCheckboxItem',
    'MenubarContent',
    'MenubarGroup',
    'MenubarItem',
    'MenubarLabel',
    'MenubarMenu',
    'MenubarPortal',
    'MenubarRadioGroup',
    'MenubarRadioItem',
    'MenubarSeparator',
    'MenubarShortcut',
    'MenubarSub',
    'MenubarSubContent',
    'MenubarSubTrigger',
    'MenubarTrigger',
    //NavigationMenu
    'NavigationMenu',
    'NavigationMenuContent',
    'NavigationMenuIndicator',
    'NavigationMenuItem',
    'NavigationMenuLink',
    'NavigationMenuList',
    'NavigationMenuTrigger',
    'NavigationMenuViewport',
    //NumberField
    'NumberField',
    'NumberFieldContent',
    'NumberFieldDecrement',
    'NumberFieldIncrement',
    'NumberFieldInput',
    //Pagination
    'Pagination',
    'PaginationContent',
    'PaginationEllipsis',
    'PaginationFirst',
    'PaginationItem',
    'PaginationLast',
    'PaginationNext',
    'PaginationPrevious',
    //PinInput
    'PinInput',
    'PinInputGroup',
    'PinInputSeparator',
    'PinInputSlot',
    //Popover
    'Popover',
    'PopoverAnchor',
    'PopoverContent',
    'PopoverTrigger',
    //RadioGroup
    'RadioGroup',
    'RadioGroupItem',
    //RangeCalendar
    'RangeCalendar',
    'RangeCalendarCell',
    'RangeCalendarCellTrigger',
    'RangeCalendarGrid',
    'RangeCalendarGridBody',
    'RangeCalendarGridHead',
    'RangeCalendarGridRow',
    'RangeCalendarHeadCell',
    'RangeCalendarHeader',
    'RangeCalendarHeading',
    'RangeCalendarNextButton',
    'RangeCalendarPrevButton',
    //Resizable
    'ResizableHandle',
    'ResizablePanel',
    'ResizablePanelGroup',
    //ScrollArea
    'ScrollArea',
    'ScrollBar',
    //Select
    'Select',
    'SelectContent',
    'SelectGroup',
    'SelectItem',
    'SelectItemText',
    'SelectLabel',
    'SelectScrollDownButton',
    'SelectScrollUpButton',
    'SelectSeparator',
    'SelectTrigger',
    'SelectValue',
    //Stepper
    'Stepper',
    'StepperDescription',
    'StepperIndicator',
    'StepperItem',
    'StepperSeparator',
    'StepperTitle',
    'StepperTrigger',
    //Table
    'Table',
    'TableBody',
    'TableCaption',
    'TableCell',
    'TableEmpty',
    'TableHead',
    'TableFooter',
    'TableHeader',
    'TableRow',
    //Tabs
    'Tabs',
    'TabsContent',
    'TabsList',
    'TabsTrigger',
    //TagsInput
    'TagsInput',
    'TagsInputInput',
    'TagsInputItem',
    'TagsInputItemDelete',
    'TagsInputItemText',
    //Toggle
    'Toggle',
    //ToggleGroup
    'ToggleGroup',
    'ToggleGroupItem',
  ]

  return [
    // 自动导入 Vue API 和工具函数
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        '@vueuse/core',
        {
          '@mattverse/shared': ['generateId', 'formatDate', 'debounce'],
          '@mattverse/mattverse-ui': ['useSidebar', 'useCarousel'],
          '@mattverse/i18n': ['useI18n', 'SUPPORTED_LOCALES', 'getBrowserLocale', 'setDocumentLang'],
        },
      ],
      dts: dtsPath,
      eslintrc: {
        enabled: true,
        filepath: './.eslintrc-auto-import.json',
      },
      vueTemplate: true,
    }),

    // 自动导入组件
    Components({
      // 自动导入 UI 组件库的组件
      resolvers: [
        // Mattverse UI 组件解析器
        (componentName: string) => {
          if (uiComponents.includes(componentName)) {
            // console.log('componentName==============', componentName)
            return {
              name: componentName,
              from: '@mattverse/mattverse-ui',
            }
          }
          return null
        },

        // Lucide 图标解析器 - 只匹配特定的图标模式，避免与其他组件冲突
        (componentName: string) => {
          // 排除 Vue Router 组件
          const vueRouterComponents = ['RouterView', 'RouterLink']
          if (vueRouterComponents.includes(componentName)) {
            return null
          }

          // 只匹配明确的图标命名模式，如 ChevronDown, ArrowRight 等
          // 但排除常见的 Vue 组件名
          if (
            componentName.match(/^[A-Z][a-z]+([A-Z][a-z]*)*$/) &&
            !uiComponents.includes(componentName) &&
            !componentName.startsWith('Router') &&
            !componentName.startsWith('Transition')
          ) {
            return {
              name: componentName,
              from: 'lucide-vue-next',
            }
          }
          return null
        },
      ],

      // 包含的文件类型
      include: [/\.vue$/, /\.vue\?vue/, /\.md$/],

      // 生成类型声明文件
      dts: './src/components.d.ts',

      // 自定义组件目录
      dirs: [
        resolve(__dirname, `../../../../apps/${appName}/src/renderer/src/components`),
        resolve(__dirname, `../../../../apps/${appName}/src/renderer/src/views`),
      ],

      // 深度搜索子目录
      deep: true,

      // 组件名转换
      directoryAsNamespace: false,
      globalNamespaces: [],
    }),
  ]
}

/**
 * 预设配置工厂函数
 * 应用可以使用这些工厂函数创建自己的配置
 */
export const autoImportPresets = {
  /** 默认配置 */
  default: (appName: string, dtsPath = 'src/auto-imports.d.ts') =>
    createAutoImportPlugins({ appName, dtsPath }),

  /** 自定义配置 */
  custom: (options: { appName: string; dtsPath?: string }) =>
    createAutoImportPlugins(options),
}
