// 设置相关
export default {
  // 基本设置
  title: '系统设置',
  basic_desc: '管理应用的基本配置和偏好设置',
  language: '语言设置',
  theme: '主题设置',
  appearance: '外观设置',
  appearance_desc: '自定义应用的外观和语言设置',
  cache: '缓存设置',
  cache_desc: '管理应用缓存和存储设置',
  notifications: '通知设置',
  notifications_desc: '管理应用通知的显示方式和类型',
  advanced: '高级设置',
  advanced_desc: '配置高级功能和系统行为',

  // 语言设置
  language_options: {
    auto: '自动检测',
    zh_cn: '简体中文',
    en_us: 'English',
  },

  // 主题设置
  theme_options: {
    light: '浅色主题',
    dark: '深色主题',
    system: '跟随系统',
    current_display: '当前显示',
  },

  // 主题色彩
  theme_colors: '主题色彩',
  use_custom_colors: '使用自定义色彩',
  cancel_custom_colors: '取消自定义',
  using_default_neutral_colors: '正在使用默认中性色彩',
  select_custom_color_or_cancel: '选择自定义色彩，或点击"取消自定义"回到默认色彩',
  color_themes: {
    'city-light': '城市-浅色',
    'forest-light': '森林-浅色',
    'lake-light': '湖水-浅色',
    'desert-light': '沙漠-浅色',
    'farm-light': '农场-浅色',
    'garden-light': '花园-浅色',
    'city-dark': '城市-深色',
    'forest-dark': '森林-深色',
    'lake-dark': '湖水-深色',
    'desert-dark': '沙漠-深色',
    'farm-dark': '农场-深色',
    'garden-dark': '花园-深色',
  },

  // 字体设置
  font_family: '字体族',
  font_size: '字号',
  font_category: '字体分类',
  font_preview: '字体预览',
  font_preview_title: '字体预览效果',
  font_preview_text: '这是字体预览文本，用于展示当前选择的字体效果。',
  font_size_preview: '字号对比',

  // 字体分类
  font_categories: {
    sans: '无衬线字体',
    serif: '衬线字体',
    mono: '等宽字体',
  },

  // 字体选项
  font_options: {
    system: '系统字体',
    default: '默认字体',
    noto: 'Noto Sans SC',
    noto_light: 'Noto Sans SC 细体',
    noto_medium: 'Noto Sans SC 中等',
    noto_extrabold: 'Noto Sans SC 特粗',
    source_han_sc: 'Source Han Sans SC',
    alibaba_puhuiti: '阿里巴巴普惠体',
    alibaba_hk: '阿里巴巴香港体',
    serif: '衬线字体',
    monospace: '等宽字体',
  },

  // 缓存设置
  cache_options: {
    title: '缓存设置',
    description: '管理应用缓存数据，清除不需要的缓存',
    enable_cache: '启用缓存',
    enable_cache_desc: '启用缓存可以提高应用性能',

    // 存储类型
    storage_types: '存储类型',
    http_cache: 'HTTP 缓存',
    http_cache_desc: '存储长期缓存的数据，如用户偏好设置',
    indexeddb: 'IndexedDB 数据库',
    indexeddb_desc: '客户端数据库，用于存储大量结构化数据',
    localstorage: 'LocalStorage',
    localstorage_desc: '网页存储在浏览器中的小型数据',
    sessionstorage: 'SessionStorage',
    sessionstorage_desc: '存储临时会话数据，浏览器关闭后删除',
    cookies: 'Cookie',
    cookies_desc: '网页存储在浏览器中的小型数据',
    appcache: '应用缓存',
    appcache_desc: '离线访问网页应用的缓存机制',
    serviceworkers: 'Service Workers',
    serviceworkers_desc: '后台脚本，用于缓存和离线网页请求',
    pinia_store: 'Pinia 状态库',
    pinia_store_desc: '应用状态管理数据',
    app_settings_cache: '应用设置缓存',
    app_settings_cache_desc: '应用配置和用户设置数据',

    // 设置项
    max_size: '最大大小',
    entries: '条目',

    // 选择清理
    select_to_clear: '选择要清理的缓存',
    app_settings: '应用设置',
    theme_settings: '主题设置',
    theme_settings_desc: '应用主题和颜色配置',
    language_settings: '语言设置',
    language_settings_desc: '界面语言偏好',
    font_settings: '字体设置',
    font_settings_desc: '应用字体大小和样式',
    workflow_data: '工作流数据',
    workflow_data_desc: '工作流文件和作业信息',
    navigation_history: '导航栏',
    navigation_history_desc: '工作流导航和历史',
    tool_settings: '工具设置',
    tool_settings_desc: '工具状态和配置数据',

    // 自动清理
    auto_clean: '自动清理',
    enable_auto_clean: '启用自动清理',
    auto_clean_desc: '定期自动清理过期的缓存数据',
    clean_interval: '清理间隔',
    daily: '每天',
    weekly: '每周',
    monthly: '每月',
    max_age: '最大保留时间',
    days: '天',

    // 退出清理
    clear_on_exit: '退出时清理缓存',
    clear_on_exit_desc: '应用退出时自动清理所有缓存',

    // 操作按钮
    cache_actions: '缓存操作',
    clear_selected: '清理选中',
    clear_all: '清理所有',
    refresh_info: '刷新信息',

    // 缓存信息
    cache_info: '缓存信息',
    last_updated: '最后更新',
    total_cache_size: '总缓存大小',

    // 消息
    selected_cache_cleared: '选中的缓存已清理',
    all_cache_cleared: '所有缓存已清理',
    cache_cleared_success: '缓存清理成功',
    cache_cleared_failed: '缓存清理失败',
    get_cache_info_failed: '获取缓存信息失败',
  },

  // 通知设置
  notification_options: {
    enable_notifications: '启用通知',
    enable_notifications_desc: '允许应用发送通知消息',
    desktop_notifications: '桌面通知',
    desktop_notifications_desc: '在桌面显示通知弹窗',
    sound_notifications: '声音通知',
    sound_notifications_desc: '通知时播放提示音',
    email_notifications: '邮件通知',
    email_notifications_desc: '通过邮件发送重要通知',
  },

  // 高级设置
  advanced_options: {
    debug_mode: '调试模式',
    debug_mode_desc: '启用调试模式以获取详细的错误信息',
    auto_save: '自动保存',
    auto_save_desc: '自动保存工作进度和设置更改',
    backup_frequency: '备份频率',
    cache_size: '缓存大小',
    log_level: '日志级别',
  },

  // 备份选项
  backup_options: {
    never: '从不备份',
    daily: '每日备份',
    weekly: '每周备份',
    monthly: '每月备份',
  },

  // 操作消息
  export_settings: '导出设置',
  export_success: '设置导出成功',
  export_failed: '设置导出失败',
  reset_to_defaults: '恢复默认设置',
  reset_success: '设置已恢复为默认值',
  save_success: '设置保存成功',

  // 关于信息
  about: {
    title: '关于',
    subtitle: '应用程序信息和系统详情',
    app_info: '应用信息',
    system_info: '系统信息',
    development_team: '开发团队',
    open_source_license: '开源许可',
    related_links: '相关链接',

    // 应用信息
    app_name: 'MattVerse',
    app_description: '一个现代化的桌面应用程序，为用户提供优质的体验和强大的功能。',
    version: '版本',
    build_type: '构建类型',
    development: '开发版',
    production: '生产版',

    // 系统信息
    operating_system: '操作系统',
    architecture: '架构',
    nodejs_version: 'Node.js 版本',
    electron_version: 'Electron 版本',
    chrome_version: 'Chrome 版本',

    // 开发团队
    chief_developer: '首席开发者',

    // 许可证
    license_type: '许可证类型',
    license_description: '本软件基于 MIT 许可证开源，允许自由使用、修改和分发。',
    view_license: '查看许可证',

    // 操作按钮
    check_updates: '检查更新',
    official_website: '官方网站',

    // 链接
    github: 'GitHub',
    documentation: '文档',
    support: '支持',
    feedback: '反馈',

    // 版权信息
    copyright: '版权信息',
    website: '官方网站',
  },

  // 中台设置
  middleware: {
    title: '中台设置',
    subtitle: '配置中台服务器连接和相关设置',
    connection_config: '连接配置',
    connection_config_desc: '配置中台服务器的连接参数',
    connection_status: '连接状态',
    advanced_settings: '高级设置',
    advanced_settings_desc: '配置连接超时和重连等高级选项',

    // 连接配置
    host: '服务器地址',
    host_placeholder: '请输入服务器地址',
    host_required: '服务器地址不能为空',
    host_invalid: '请输入有效的IP地址或域名',
    port: '端口',
    port_placeholder: '请输入端口号',
    test_connection: '测试连接',

    // 连接状态
    status: '连接状态',
    current_address: '当前服务地址',
    version: '中台版本',

    // 状态文本
    status_idle: '未连接',
    status_connecting: '连接中',
    status_ready: '已连接',
    status_failed: '连接失败',
    status_shutdown: '已断开',

    // 高级设置
    connection_timeout: '连接超时',
    auto_reconnect: '自动重连',
    auto_reconnect_desc: '连接断开时自动尝试重新连接',

    // 确认对话框
    confirm_changes: '确认更改',
    confirm_changes_desc: '更改连接配置将需要重启应用程序才能生效',
    new_host: '新服务器地址',
    new_port: '新端口',
    restart_warning: '点击确认后，应用程序将自动重启以应用新的配置。',
    restart_confirm: '配置已保存，是否立即重启应用以应用新配置？',

    // 提示消息
    test_connection_success: '连接测试成功',
    test_connection_failed: '连接测试失败',
    config_saved: '配置保存成功',
    config_save_failed: '配置保存失败',
    restarting_app: '应用程序正在重启...',
    restart_success: '应用程序重启成功',
    restart_failed: '应用程序重启失败',
    manual_restart_required: '开发环境下请手动重启应用以避免白屏问题',
    app_will_close: '应用程序将在1秒后关闭，请手动重启',
    init_failed: '初始化中台设置失败',
  },
}
