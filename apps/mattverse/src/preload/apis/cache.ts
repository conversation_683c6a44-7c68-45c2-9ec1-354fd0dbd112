/**
 * 缓存相关 API
 */
import { ipcRenderer } from 'electron'
import type { CacheType, CacheInfo } from '@mattverse/electron-core'

/**
 * 缓存管理 API
 */
export const cacheAPI = {
  /**
   * 获取所有缓存信息
   */
  getCacheInfo: (): Promise<Record<string, CacheInfo>> => 
    ipcRenderer.invoke('cache:get-info'),

  /**
   * 清理指定类型的缓存
   */
  clearCacheTypes: (types: CacheType[]): Promise<{ success: boolean; cleared: CacheType[] }> => 
    ipcRenderer.invoke('cache:clear-types', types),

  /**
   * 清理所有缓存
   */
  clearAllCache: (): Promise<{ success: boolean }> => 
    ipcRenderer.invoke('cache:clear-all'),

  /**
   * 设置缓存大小限制
   */
  setCacheSizeLimit: (type: CacheType, sizeMB: number): Promise<{ success: boolean }> => 
    ipcRenderer.invoke('cache:set-size-limit', type, sizeMB),
}
