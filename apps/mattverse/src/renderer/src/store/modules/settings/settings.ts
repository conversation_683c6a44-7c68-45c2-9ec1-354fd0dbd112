import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { useColorMode, useDark, useToggle } from '@vueuse/core'
import { createPersistConfig } from '@/store/plugins/persist-config'
import { setDocumentLang, type SupportedLocale } from '@mattverse/i18n'

/**
 * 主题模式类型
 */
export type ThemeMode = 'light' | 'dark' | 'system'

/**
 * 颜色主题类型
 */
export type ColorTheme =
  | 'neutral' // 默认中性色彩
  | 'city-light'
  | 'forest-light'
  | 'lake-light'
  | 'desert-light'
  | 'farm-light'
  | 'garden-light'
  | 'city-dark'
  | 'forest-dark'
  | 'lake-dark'
  | 'desert-dark'
  | 'farm-dark'
  | 'garden-dark'

/**
 * 语言代码类型
 */
export type LanguageCode = SupportedLocale

/**
 * 字体设置接口
 */
export interface FontSettings {
  fontFamily: string
  fontSize: number
}

/**
 * 缓存类型枚举
 */
export type CacheType =
  | 'http'
  | 'indexeddb'
  | 'localstorage'
  | 'sessionstorage'
  | 'cookies'
  | 'appcache'
  | 'serviceworkers'
  | 'pinia'
  | 'app-settings'

/**
 * 单个缓存类型配置
 */
export interface CacheTypeConfig {
  enabled: boolean
  maxSize: number // MB
  autoClean: boolean
}

/**
 * 自动清理配置
 */
export interface AutoCleanConfig {
  enabled: boolean
  interval: 'daily' | 'weekly' | 'monthly' | 'never'
  maxAge: number // 天数
}

/**
 * 缓存设置接口
 */
export interface CacheSettings {
  // 全局缓存开关
  enableCache: boolean

  // 各类型缓存配置
  types: Record<CacheType, CacheTypeConfig>

  // 自动清理配置
  autoClean: AutoCleanConfig

  // 退出时清理
  clearOnExit: boolean

  // 显示缓存信息
  showCacheInfo: boolean
}

/**
 * 通知设置接口
 */
export interface NotificationSettings {
  enableNotifications: boolean
  desktopNotifications: boolean
  soundNotifications: boolean
  emailNotifications: boolean
}

/**
 * 高级设置接口
 */
export interface AdvancedSettings {
  debugMode: boolean
  autoSave: boolean
  backupFrequency: 'never' | 'daily' | 'weekly' | 'monthly'
  cacheSize: number // MB
  logLevel: 'error' | 'warn' | 'info' | 'debug'
}

/**
 * 设置状态接口
 */
export interface SettingsState {
  // 基本设置
  language: LanguageCode
  theme: ThemeMode
  colorTheme: ColorTheme

  // 字体设置
  font: FontSettings

  // 缓存设置
  cache: CacheSettings

  // 通知设置
  notifications: NotificationSettings

  // 高级设置
  advanced: AdvancedSettings

  // 首次启动标记
  isFirstLaunch: boolean
}

/**
 * 默认设置
 */
const defaultSettings: SettingsState = {
  language: 'zh-CN',
  theme: 'system',
  colorTheme: 'city-light',
  font: {
    fontFamily: '--font-sans',
    fontSize: 14,
  },
  cache: {
    enableCache: true,
    types: {
      // HTTP 缓存：Electron 默认无限制，建议设置合理上限
      http: { enabled: true, maxSize: 1000, autoClean: false },
      // IndexedDB：Electron 中基于 Chromium，可达磁盘空间的 80%，适合大量本地数据
      indexeddb: { enabled: true, maxSize: 10240, autoClean: false },
      // LocalStorage：单个值限制 10MB，总存储可更大，但要考虑性能
      localstorage: { enabled: true, maxSize: 50, autoClean: false },
      // SessionStorage：与 LocalStorage 类似，单个值限制 10MB
      sessionstorage: { enabled: true, maxSize: 50, autoClean: false },
      // Cookies：每个域名约 4KB 总大小，实际使用量很小
      cookies: { enabled: true, maxSize: 1, autoClean: false },
      // AppCache：已废弃，但仍可能存在
      appcache: { enabled: true, maxSize: 100, autoClean: false },
      // Service Workers：可以缓存大量资源
      serviceworkers: { enabled: true, maxSize: 500, autoClean: false },
      // Pinia Store：内存中的状态管理
      pinia: { enabled: true, maxSize: 50, autoClean: false },
      // 应用设置：配置文件
      'app-settings': { enabled: true, maxSize: 10, autoClean: false },
    },
    autoClean: {
      enabled: false,
      interval: 'weekly',
      maxAge: 7,
    },
    clearOnExit: false,
    showCacheInfo: true,
  },
  notifications: {
    enableNotifications: true,
    desktopNotifications: true,
    soundNotifications: false,
    emailNotifications: false,
  },
  advanced: {
    debugMode: false,
    autoSave: true,
    backupFrequency: 'daily',
    cacheSize: 100,
    logLevel: 'info',
  },
  isFirstLaunch: true,
}

/**
 * 设置状态管理
 */
export const useSettingsStore = defineStore(
  'settings',
  () => {
    // 基本设置状态
    const language = ref<LanguageCode>(defaultSettings.language)
    const theme = ref<ThemeMode>(defaultSettings.theme)
    const colorTheme = ref<ColorTheme>(defaultSettings.colorTheme)

    // 字体设置状态
    const font = ref<FontSettings>({ ...defaultSettings.font })

    // 缓存设置状态
    const cache = ref<CacheSettings>({ ...defaultSettings.cache })

    // 通知设置状态
    const notifications = ref<NotificationSettings>({ ...defaultSettings.notifications })

    // 高级设置状态
    const advanced = ref<AdvancedSettings>({ ...defaultSettings.advanced })

    // 首次启动标记
    const isFirstLaunch = ref<boolean>(defaultSettings.isFirstLaunch)

    // 使用 VueUse 的主题管理
    const colorMode = useColorMode({
      initialValue: theme.value === 'system' ? 'auto' : theme.value,
    })

    const isDark = useDark({
      selector: 'html',
      attribute: 'class',
      valueDark: 'dark',
      valueLight: 'light',
    })

    const toggleDark = useToggle(isDark)

    // 计算属性
    const currentTheme = computed(() => {
      if (theme.value === 'system') {
        return isDark.value ? 'dark' : 'light'
      }
      return theme.value
    })

    const isSystemTheme = computed(() => theme.value === 'system')

    // 监听主题变化
    watch(
      theme,
      newTheme => {
        if (newTheme === 'system') {
          colorMode.value = 'auto'
        } else {
          colorMode.value = newTheme
          isDark.value = newTheme === 'dark'
        }
      },
      { immediate: true }
    )

    // 监听语言变化
    watch(
      language,
      newLanguage => {
        // 设置文档语言属性
        setDocumentLang(newLanguage)
      },
      { immediate: true }
    )

    // 方法
    const setLanguage = (newLanguage: LanguageCode) => {
      language.value = newLanguage
    }

    const setTheme = (newTheme: ThemeMode) => {
      theme.value = newTheme
    }

    const setColorTheme = (newColorTheme: ColorTheme) => {
      colorTheme.value = newColorTheme
      // 应用对应的主题色彩 CSS 变量
      applyColorThemeVariables(newColorTheme)
    }

    // 主题色彩配置映射（提取为常量，避免每次调用都创建）
    const COLOR_THEME_MAP: Record<Exclude<ColorTheme, 'neutral'>, string> = {
      'city-light': '187 25.9% 87.5%',
      'forest-light': '49 47.4% 78.2%',
      'lake-light': '243 17.3% 82.2%',
      'desert-light': '26 96.2% 84.1%',
      'farm-light': '222 47.4% 85.3%',
      'garden-light': '37 98.2% 85.1%',
      'city-dark': '197 22.9% 21.2%',
      'forest-dark': '79 22.4% 16.2%',
      'lake-dark': '220 42.4% 20.2%',
      'desert-dark': '26 32.2% 18.2%',
      'farm-dark': '69 22.4% 15.2%',
      'garden-dark': '315 22.4% 15.2%',
    }

    // 应用主题色彩变量的函数
    const applyColorThemeVariables = (themeValue: ColorTheme) => {
      const root = document.documentElement

      // 如果是 neutral 主题，重置为默认色彩
      if (themeValue === 'neutral') {
        root.style.removeProperty('--primary')
        root.style.removeProperty('--primary-foreground')
        root.removeAttribute('data-theme')
        return
      }

      // 应用自定义色彩
      const primaryColor = COLOR_THEME_MAP[themeValue as Exclude<ColorTheme, 'neutral'>]
      if (!primaryColor) return

      // 设置 data-theme 属性
      root.setAttribute('data-theme', themeValue)

      // 设置主色调
      root.style.setProperty('--primary', primaryColor)

      // 设置前景色
      const [h, s] = primaryColor.split(' ')
      const lightness = themeValue.endsWith('-light') ? '20%' : '95%'
      root.style.setProperty('--primary-foreground', `${h} ${s} ${lightness}`)
    }

    const toggleTheme = () => {
      if (theme.value === 'light') {
        setTheme('dark')
      } else if (theme.value === 'dark') {
        setTheme('system')
      } else {
        setTheme('light')
      }
    }

    const updateFont = (updates: Partial<FontSettings>) => {
      font.value = { ...font.value, ...updates }
    }

    const updateCache = (updates: Partial<CacheSettings>) => {
      cache.value = { ...cache.value, ...updates }
    }

    const updateNotifications = (updates: Partial<NotificationSettings>) => {
      notifications.value = { ...notifications.value, ...updates }
    }

    const updateAdvanced = (updates: Partial<AdvancedSettings>) => {
      advanced.value = { ...advanced.value, ...updates }
    }

    const resetToDefaults = () => {
      language.value = defaultSettings.language
      theme.value = defaultSettings.theme
      colorTheme.value = defaultSettings.colorTheme
      font.value = { ...defaultSettings.font }
      cache.value = { ...defaultSettings.cache }
      notifications.value = { ...defaultSettings.notifications }
      advanced.value = { ...defaultSettings.advanced }
    }

    const markAsLaunched = () => {
      isFirstLaunch.value = false
    }

    // 导出设置数据
    const exportSettings = () => {
      return {
        language: language.value,
        theme: theme.value,
        colorTheme: colorTheme.value,
        font: font.value,
        cache: cache.value,
        notifications: notifications.value,
        advanced: advanced.value,
        exportTime: new Date().toISOString(),
      }
    }

    // 导入设置数据
    const importSettings = (settingsData: Partial<SettingsState>) => {
      if (settingsData.language) {
        setLanguage(settingsData.language)
      }
      if (settingsData.theme) {
        setTheme(settingsData.theme)
      }
      if (settingsData.colorTheme) {
        setColorTheme(settingsData.colorTheme)
      }
      if (settingsData.font) {
        updateFont(settingsData.font)
      }
      if (settingsData.cache) {
        updateCache(settingsData.cache)
      }
      if (settingsData.notifications) {
        updateNotifications(settingsData.notifications)
      }
      if (settingsData.advanced) {
        updateAdvanced(settingsData.advanced)
      }
    }

    // 应用字体设置到全局的函数
    const applyFontSettings = (fontSettings: FontSettings) => {
      const root = document.documentElement

      // 获取字体的 CSS 变量和权重
      const getFontInfo = (fontValue: string) => {
        const fontMap: Record<string, { family: string; weight?: string }> = {
          'system-ui': { family: 'system-ui, -apple-system, sans-serif' },
          '--font-sans': { family: 'var(--font-sans)' },
          '--font-serif': { family: 'var(--font-serif)' },
          '--font-mono': { family: 'var(--font-mono)' },
          '--font-noto': { family: "'NotoSansSC', sans-serif", weight: '400' },
          '--font-noto-light': { family: "'NotoSansSC', sans-serif", weight: '300' },
          '--font-noto-regular': { family: "'NotoSansSC', sans-serif", weight: '400' },
          '--font-noto-medium': { family: "'NotoSansSC', sans-serif", weight: '500' },
          '--font-noto-extrabold': { family: "'NotoSansSC', sans-serif", weight: '800' },
          '--font-noto-black': { family: "'NotoSansSC', sans-serif", weight: '900' },
          '--font-alibaba-puhuiti': { family: "'AlibabaPuHuiTi', sans-serif" },
          '--font-alibaba-hk': { family: "'AlibabaSansHK', sans-serif" },
          '--font-source-han-sc': { family: "'SourceHanSansSC', sans-serif" },
          '--font-source-han-tc': { family: "'SourceHanSansTC', sans-serif" },
          '--font-source-han-hc': { family: "'SourceHanSansHC', sans-serif" },
          '--font-source-han-hk': { family: "'SourceHanSansHK', sans-serif" },
          '--font-source-han-k': { family: "'SourceHanSansK', sans-serif" },
        }

        return fontMap[fontValue] || { family: fontValue }
      }

      const fontInfo = getFontInfo(fontSettings.fontFamily)

      // 设置全局字体变量
      root.style.setProperty('--font-display', fontInfo.family)
      root.style.setProperty('--base-font-size', `${fontSettings.fontSize}px`)

      // 更新 body 样式
      if (document.body) {
        document.body.style.fontFamily = fontInfo.family
        document.body.style.fontSize = `${fontSettings.fontSize}px`
        if (fontInfo.weight) {
          document.body.style.fontWeight = fontInfo.weight
        }
      }

      // 添加调试信息
      console.log('Applied font settings:', {
        fontFamily: fontInfo.family,
        fontSize: `${fontSettings.fontSize}px`,
        fontWeight: fontInfo.weight || 'normal',
      })
    }

    // 监听字体设置变化 - 在函数定义之后
    watch(
      font,
      newFont => {
        // 应用字体设置到全局
        applyFontSettings(newFont)
      },
      { deep: true, immediate: true }
    )

    // 监听颜色主题变化并应用到 DOM
    watch(colorTheme, applyColorThemeVariables, { immediate: true })

    return {
      // 状态
      language,
      theme,
      colorTheme,
      font,
      cache,
      notifications,
      advanced,
      isFirstLaunch,

      // VueUse 相关
      colorMode,
      isDark,
      toggleDark,

      // 计算属性
      currentTheme,
      isSystemTheme,

      // 方法
      setLanguage,
      setTheme,
      setColorTheme,
      toggleTheme,
      updateFont,
      updateCache,
      updateNotifications,
      updateAdvanced,
      resetToDefaults,
      markAsLaunched,
      exportSettings,
      importSettings,
      applyFontSettings,
      applyColorThemeVariables,
    }
  },
  {
    persist: createPersistConfig('settings'),
  }
)
