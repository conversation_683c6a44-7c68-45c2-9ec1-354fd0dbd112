<template>
  <div class="space-y-6">
    <!-- 全局缓存开关 -->
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <Label class="text-sm font-medium">{{ $t('settings.cache_options.enable_cache') }}</Label>
        <p class="text-xs text-muted-foreground">
          {{ $t('settings.cache_options.enable_cache_desc') }}
        </p>
      </div>
      <Switch :model-value="cacheSettings.enableCache" @update:model-value="updateGlobalCache" />
    </div>

    <Separator />

    <!-- 存储类型管理 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-lg font-medium">{{ $t('settings.cache_options.storage_types') }}</Label>
        <div class="flex gap-2">
          <Button variant="outline" size="sm" @click="selectAllTypes">
            {{ $t('common.select_all') }}
          </Button>
          <Button variant="outline" size="sm" @click="deselectAllTypes">
            {{ $t('common.deselect_all') }}
          </Button>
        </div>
      </div>

      <!-- 缓存类型列表 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div
          v-for="(config, type) in cacheSettings.types"
          :key="type"
          class="rounded-lg border p-4 space-y-3"
          :class="{
            'bg-muted/30': !config.enabled || !cacheSettings.enableCache,
            'bg-background': config.enabled && cacheSettings.enableCache,
          }"
        >
          <!-- 类型标题和开关 -->
          <div class="flex items-center justify-between">
            <div class="space-y-1">
              <div class="flex items-center gap-2">
                <Checkbox
                  :model-value="config.enabled"
                  :disabled="!cacheSettings.enableCache"
                  @update:model-value="updateCacheType(type, 'enabled', $event)"
                />
                <Label class="text-sm font-medium">
                  {{ getCacheTypeLabel(type) }}
                </Label>
              </div>
              <p class="text-xs text-muted-foreground">
                {{ getCacheTypeDescription(type) }}
              </p>
            </div>
            <div class="text-right">
              <div class="text-sm font-mono text-muted-foreground">
                {{ cacheInfo[type]?.size || 0 }}MB
              </div>
              <div class="text-xs text-muted-foreground">
                {{ cacheInfo[type]?.entries || 0 }} {{ $t('settings.cache_options.entries') }}
              </div>
            </div>
          </div>

          <!-- 大小设置 -->
          <div v-if="config.enabled && cacheSettings.enableCache" class="space-y-2">
            <div class="flex items-center justify-between">
              <Label class="text-xs">{{ $t('settings.cache_options.max_size') }}</Label>
              <span class="text-xs text-muted-foreground">{{ config.maxSize }}MB</span>
            </div>
            <Slider
              :model-value="[config.maxSize]"
              :max="getMaxSizeForType(type)"
              :min="1"
              :step="1"
              class="flex-1"
              @update:model-value="updateCacheType(type, 'maxSize', $event[0])"
            />
          </div>
        </div>
      </div>
    </div>

    <Separator />

    <!-- 选择要清理的缓存 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-lg font-medium">{{
          $t('settings.cache_options.select_to_clear')
        }}</Label>
        <div class="flex gap-2">
          <Button variant="outline" size="sm" @click="selectAllForClear">
            {{ $t('common.select_all') }}
          </Button>
          <Button variant="outline" size="sm" @click="deselectAllForClear">
            {{ $t('common.deselect_all') }}
          </Button>
        </div>
      </div>

      <!-- 应用设置缓存 -->
      <div class="space-y-3">
        <Label class="text-sm font-medium">{{ $t('settings.cache_options.app_settings') }}</Label>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div
            v-for="settingType in appSettingTypes"
            :key="settingType.key"
            class="flex items-center justify-between p-3 rounded-lg border"
          >
            <div class="flex items-center gap-2">
              <Checkbox
                v-model="selectedForClear[settingType.key]"
                :disabled="!cacheSettings.enableCache"
              />
              <div>
                <Label class="text-sm">{{ settingType.label }}</Label>
                <p class="text-xs text-muted-foreground">{{ settingType.description }}</p>
              </div>
            </div>
            <div class="text-xs text-muted-foreground">
              {{ settingType.size }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自动清理设置 -->
    <div class="space-y-4">
      <Label class="text-lg font-medium">{{ $t('settings.cache_options.auto_clean') }}</Label>

      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm font-medium">{{
            $t('settings.cache_options.enable_auto_clean')
          }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.cache_options.auto_clean_desc') }}
          </p>
        </div>
        <Switch
          :model-value="cacheSettings.autoClean.enabled"
          :disabled="!cacheSettings.enableCache"
          @update:model-value="updateAutoClean('enabled', $event)"
        />
      </div>

      <div v-if="cacheSettings.autoClean.enabled && cacheSettings.enableCache" class="space-y-4">
        <div class="space-y-2">
          <Label class="text-sm">{{ $t('settings.cache_options.clean_interval') }}</Label>
          <Select
            :model-value="cacheSettings.autoClean.interval"
            @update:model-value="updateAutoClean('interval', $event)"
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">{{ $t('settings.cache_options.daily') }}</SelectItem>
              <SelectItem value="weekly">{{ $t('settings.cache_options.weekly') }}</SelectItem>
              <SelectItem value="monthly">{{ $t('settings.cache_options.monthly') }}</SelectItem>
              <SelectItem value="never">{{ $t('common.never') }}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <Label class="text-sm">{{ $t('settings.cache_options.max_age') }}</Label>
            <span class="text-sm text-muted-foreground"
              >{{ cacheSettings.autoClean.maxAge }} {{ $t('settings.cache_options.days') }}</span
            >
          </div>
          <Slider
            :model-value="[cacheSettings.autoClean.maxAge]"
            :max="90"
            :min="1"
            :step="1"
            @update:model-value="updateAutoClean('maxAge', $event[0])"
          />
        </div>
      </div>
    </div>

    <Separator />

    <!-- 退出时清理 -->
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <Label class="text-sm font-medium">{{ $t('settings.cache_options.clear_on_exit') }}</Label>
        <p class="text-xs text-muted-foreground">
          {{ $t('settings.cache_options.clear_on_exit_desc') }}
        </p>
      </div>
      <Switch
        :model-value="cacheSettings.clearOnExit"
        :disabled="!cacheSettings.enableCache"
        @update:model-value="updateCache('clearOnExit', $event)"
      />
    </div>

    <Separator />

    <!-- 缓存操作 -->
    <div class="space-y-4">
      <Label class="text-lg font-medium">{{ $t('settings.cache_options.cache_actions') }}</Label>

      <div class="flex flex-wrap gap-2">
        <Button
          variant="outline"
          size="sm"
          :disabled="!cacheSettings.enableCache || !hasSelectedForClear"
          @click="clearSelectedCache"
        >
          <MattIcon name="Trash2" class="mr-2 h-4 w-4" />
          {{ $t('settings.cache_options.clear_selected') }}
        </Button>
        <Button
          variant="destructive"
          size="sm"
          :disabled="!cacheSettings.enableCache"
          @click="clearAllCache"
        >
          <MattIcon name="Trash" class="mr-2 h-4 w-4" />
          {{ $t('settings.cache_options.clear_all') }}
        </Button>
        <Button variant="outline" size="sm" @click="refreshCacheInfo">
          <MattIcon name="RefreshCw" class="mr-2 h-4 w-4" />
          {{ $t('settings.cache_options.refresh_info') }}
        </Button>
      </div>
    </div>

    <!-- 缓存信息显示（默认展开） -->
    <div class="rounded-lg border bg-muted/50 p-4">
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <Label class="text-sm font-medium">{{ $t('settings.cache_options.cache_info') }}</Label>
          <div class="text-sm text-muted-foreground">
            {{ $t('settings.cache_options.last_updated') }}: {{ lastUpdated }}
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="(info, type) in cacheInfo"
            :key="type"
            class="flex items-center justify-between p-2 rounded border bg-background"
          >
            <div>
              <div class="text-sm font-medium">{{ getCacheTypeLabel(type) }}</div>
              <div class="text-xs text-muted-foreground">
                {{ info.entries }} {{ $t('settings.cache_options.entries') }}
              </div>
            </div>
            <div class="text-sm font-mono">{{ info.size }}MB</div>
          </div>
        </div>

        <div class="pt-2 border-t">
          <div class="flex justify-between text-sm">
            <span class="font-medium">{{ $t('settings.cache_options.total_cache_size') }}:</span>
            <span class="font-mono">{{ totalCacheSize }}MB</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { useI18n } from '@mattverse/i18n'
import { useSettingsStore, type CacheSettings, type CacheType, type AutoCleanConfig } from '@/store'

const { t } = useI18n()
const settingsStore = useSettingsStore()

// 响应式数据
const selectedForClear = ref<Record<string, boolean>>({})
const cacheInfo = ref<Record<string, { size: number; entries: number }>>({})
const lastUpdated = ref<string>('')

// 计算属性
const cacheSettings = computed(() => settingsStore.cache)

const totalCacheSize = computed(() => {
  return Object.values(cacheInfo.value).reduce((total, info) => total + info.size, 0)
})

const hasSelectedForClear = computed(() => {
  return Object.values(selectedForClear.value).some(selected => selected)
})

// 应用设置类型
const appSettingTypes = computed(() => [
  {
    key: 'theme',
    label: t('settings.cache_options.theme_settings'),
    description: t('settings.cache_options.theme_settings_desc'),
    size: '0.1KB',
  },
  {
    key: 'language',
    label: t('settings.cache_options.language_settings'),
    description: t('settings.cache_options.language_settings_desc'),
    size: '0.05KB',
  },
  {
    key: 'font',
    label: t('settings.cache_options.font_settings'),
    description: t('settings.cache_options.font_settings_desc'),
    size: '0.08KB',
  },
  {
    key: 'workflow',
    label: t('settings.cache_options.workflow_data'),
    description: t('settings.cache_options.workflow_data_desc'),
    size: '2.5MB',
  },
  {
    key: 'navigation',
    label: t('settings.cache_options.navigation_history'),
    description: t('settings.cache_options.navigation_history_desc'),
    size: '0.3MB',
  },
  {
    key: 'tools',
    label: t('settings.cache_options.tool_settings'),
    description: t('settings.cache_options.tool_settings_desc'),
    size: '1.2MB',
  },
])

// 方法
const updateGlobalCache = (enabled: boolean) => {
  settingsStore.updateCache({ enableCache: enabled })
  // 同步更新到主进程的缓存管理器
  syncCacheManagerSettings()
}

const updateCache = (key: keyof CacheSettings, value: any) => {
  settingsStore.updateCache({ [key]: value })
  // 同步更新到主进程的缓存管理器
  syncCacheManagerSettings()
}

const updateCacheType = (type: string, key: string, value: any) => {
  const currentTypes = { ...cacheSettings.value.types }
  currentTypes[type as CacheType] = {
    ...currentTypes[type as CacheType],
    [key]: value,
  }
  settingsStore.updateCache({ types: currentTypes })
}

const updateAutoClean = (key: keyof AutoCleanConfig, value: boolean | string | number) => {
  const currentAutoClean = { ...cacheSettings.value.autoClean }
  ;(currentAutoClean as any)[key] = value
  settingsStore.updateCache({ autoClean: currentAutoClean })

  // 同步更新到主进程的缓存管理器
  syncCacheManagerSettings()
}

// 同步缓存管理器设置到主进程
const syncCacheManagerSettings = async () => {
  try {
    // 使用 JSON 序列化/反序列化来完全移除响应式代理
    const rawSettings = {
      enableCache: cacheSettings.value.enableCache,
      clearOnExit: cacheSettings.value.clearOnExit,
      autoClean: cacheSettings.value.autoClean,
      types: cacheSettings.value.types,
    }

    // 深度克隆，移除所有响应式代理
    const settings = JSON.parse(JSON.stringify(rawSettings))

    if (window.electronAPI.cache.updateManagerSettings) {
      await window.electronAPI.cache.updateManagerSettings(settings)
    }
  } catch {
    // 静默处理错误，避免影响用户体验
  }
}

// 选择操作
const selectAllTypes = () => {
  // 创建一个全新的 types 对象，确保响应性更新
  const newTypes = { ...cacheSettings.value.types }
  Object.keys(newTypes).forEach(type => {
    newTypes[type as CacheType] = {
      ...newTypes[type as CacheType],
      enabled: true,
    }
  })

  // 一次性更新整个 types 对象
  settingsStore.updateCache({ types: newTypes })
}

const deselectAllTypes = () => {
  // 创建一个全新的 types 对象，确保响应性更新
  const newTypes = { ...cacheSettings.value.types }
  Object.keys(newTypes).forEach(type => {
    newTypes[type as CacheType] = {
      ...newTypes[type as CacheType],
      enabled: false,
    }
  })

  // 一次性更新整个 types 对象
  settingsStore.updateCache({ types: newTypes })
}

const selectAllForClear = () => {
  const newSelection: Record<string, boolean> = {}
  appSettingTypes.value.forEach(type => {
    newSelection[type.key] = true
  })
  selectedForClear.value = newSelection
}

const deselectAllForClear = () => {
  const newSelection: Record<string, boolean> = {}
  appSettingTypes.value.forEach(type => {
    newSelection[type.key] = false
  })
  selectedForClear.value = newSelection
}

// 缓存操作
const clearSelectedCache = async () => {
  try {
    const selectedTypes = Object.entries(selectedForClear.value)
      .filter(([, selected]) => selected)
      .map(([type]) => type)

    if (selectedTypes.length === 0) return

    // 调用实际的缓存清理 API
    const result = await window.electronAPI.cache.clearCacheTypes(selectedTypes as any[])

    if (result.success) {
      // 缓存清理成功
    }
    await refreshCacheInfo()
  } catch {
    // 静默处理清理失败的情况
  }
}

const clearAllCache = async () => {
  try {
    // 调用实际的缓存清理 API
    const result = await window.electronAPI.cache.clearAllCache()

    if (result.success) {
      // 重置缓存信息
      Object.keys(cacheInfo.value).forEach(type => {
        cacheInfo.value[type] = { size: 0, entries: 0 }
      })
    }

    await refreshCacheInfo()
  } catch {
    // 静默处理清理失败的情况
  }
}

const refreshCacheInfo = async () => {
  try {
    // 调用实际的缓存信息获取 API
    const info = await window.electronAPI.cache.getCacheInfo()

    // 更新缓存信息
    cacheInfo.value = info
    lastUpdated.value = new Date().toLocaleString()
  } catch {
    // 获取缓存信息失败，使用模拟数据

    // 如果获取失败，使用模拟数据
    const mockInfo: Record<string, { size: number; entries: number }> = {}
    Object.keys(cacheSettings.value.types).forEach(type => {
      mockInfo[type] = {
        size: Math.floor(Math.random() * cacheSettings.value.types[type as CacheType].maxSize),
        entries: Math.floor(Math.random() * 1000),
      }
    })

    cacheInfo.value = mockInfo
    lastUpdated.value = new Date().toLocaleString()
  }
}

// 工具方法
const getCacheTypeLabel = (type: string): string => {
  const labels: Record<CacheType, string> = {
    http: t('settings.cache_options.http_cache'),
    indexeddb: t('settings.cache_options.indexeddb'),
    localstorage: t('settings.cache_options.localstorage'),
    sessionstorage: t('settings.cache_options.sessionstorage'),
    cookies: t('settings.cache_options.cookies'),
    appcache: t('settings.cache_options.appcache'),
    serviceworkers: t('settings.cache_options.serviceworkers'),
    pinia: t('settings.cache_options.pinia_store'),
    'app-settings': t('settings.cache_options.app_settings_cache'),
  }
  return labels[type as CacheType] || type
}

const getCacheTypeDescription = (type: string): string => {
  const descriptions: Record<CacheType, string> = {
    http: t('settings.cache_options.http_cache_desc'),
    indexeddb: t('settings.cache_options.indexeddb_desc'),
    localstorage: t('settings.cache_options.localstorage_desc'),
    sessionstorage: t('settings.cache_options.sessionstorage_desc'),
    cookies: t('settings.cache_options.cookies_desc'),
    appcache: t('settings.cache_options.appcache_desc'),
    serviceworkers: t('settings.cache_options.serviceworkers_desc'),
    pinia: t('settings.cache_options.pinia_store_desc'),
    'app-settings': t('settings.cache_options.app_settings_cache_desc'),
  }
  return descriptions[type as CacheType] || ''
}

const getMaxSizeForType = (type: string): number => {
  const maxSizes: Record<CacheType, number> = {
    http: 2000, // HTTP 缓存最大 2GB（Electron 环境宽松）
    indexeddb: 50000, // IndexedDB 最大 50GB（Electron 基于 Chromium，可达磁盘 80%）
    localstorage: 100, // LocalStorage 最大 100MB（单个值限制 10MB，总存储可更大）
    sessionstorage: 100, // SessionStorage 最大 100MB（单个值限制 10MB，总存储可更大）
    cookies: 5, // Cookies 最大 5MB（实际每域名约 4KB，但允许多个域名）
    appcache: 200, // AppCache 最大 200MB（已废弃但可能存在）
    serviceworkers: 1000, // Service Workers 最大 1GB（可缓存大量资源）
    pinia: 100, // Pinia Store 最大 100MB（内存状态管理）
    'app-settings': 20, // App Settings 最大 20MB（配置文件）
  }
  return maxSizes[type as CacheType] || 100
}

// 生命周期
onMounted(() => {
  refreshCacheInfo()

  // 初始化选择状态
  const initialSelection: Record<string, boolean> = {}
  appSettingTypes.value.forEach(type => {
    initialSelection[type.key] = false
  })
  selectedForClear.value = initialSelection

  // 同步当前设置到缓存管理器
  syncCacheManagerSettings()
})
</script>
