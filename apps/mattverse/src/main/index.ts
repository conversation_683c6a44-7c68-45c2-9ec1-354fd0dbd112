/**
 * Mattverse 主进程
 */
import { config } from 'dotenv'
import { join, dirname } from 'node:path'
import { fileURLToPath } from 'node:url'
import { createElectronApp, logger, cacheManager } from '@mattverse/electron-core'
import { handlers, initializeGrpcClient, setGrpcClient } from './handlers'

// 加载环境变量
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const rootDir = join(__dirname, '../../../..')
config({ path: join(rootDir, '.env.development') })

// 创建 Mattverse 应用
const app = createElectronApp({
  window: {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: false,
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
    },
  },
  // 配置开发者工具
  devTools: {
    enabled: true,
    extensions: ['VUE_DEVTOOLS'], // 安装 Vue 开发者工具
    autoOpen: true, // 开发环境自动打开
    forceInProduction: false, // 生产环境不启用
  },
  handlers,
  onReady: () => {
    logger.info('Mattverse app is ready!')

    // 初始化 gRPC 客户端
    try {
      const grpcClient = initializeGrpcClient()
      if (grpcClient) {
        setGrpcClient(grpcClient)
        logger.info('Mattverse gRPC 客户端已在应用启动时初始化')
      }
    } catch (error) {
      logger.error('Mattverse 应用启动时 gRPC 客户端初始化失败:', error)
    }

    // 初始化缓存管理器（使用默认设置）
    try {
      cacheManager.initialize({
        enableCache: true,
        clearOnExit: false,
        autoClean: {
          enabled: false,
          interval: 'weekly',
          maxAge: 7,
        },
        types: {
          http: { enabled: true, maxSize: 1000, autoClean: false },
          indexeddb: { enabled: true, maxSize: 10240, autoClean: false },
          localstorage: { enabled: true, maxSize: 50, autoClean: false },
          sessionstorage: { enabled: true, maxSize: 50, autoClean: false },
          cookies: { enabled: true, maxSize: 1, autoClean: false },
          appcache: { enabled: true, maxSize: 100, autoClean: false },
          serviceworkers: { enabled: true, maxSize: 500, autoClean: false },
          pinia: { enabled: true, maxSize: 50, autoClean: false },
          'app-settings': { enabled: true, maxSize: 10, autoClean: false },
        },
      })
      logger.info('缓存管理器已初始化')
    } catch (error) {
      logger.error('缓存管理器初始化失败:', error)
    }
  },
  },
  onWindowCreated: () => {
    logger.info('Mattverse window created!')
  },
})

// 启动应用
app.start().catch(error => {
  logger.error('Failed to start Mattverse app:', error)
  process.exit(1)
})
